#pragma once

#include "iceoryx_hoofs/cxx/vector.hpp"

#include <chrono>
#include <cstdint>

#ifdef __cplusplus
extern "C" {
#endif

/* 草区域最多单元格数量，65205 = 255 * 255 */
#define IOX_MAX_CELL_NUM 65205

/**
 * @brief 草区域单元格类型
 */
typedef enum fescue_msgs_enum__GrassCellType
{
    FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_GRASS = 0,            /* 草 */
    FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_UNKNOWN_OBSTACLE = 1, /* 未知类别障碍物 */

    /* 动态障碍物(特别注意此处枚举值与目标检测中的类别ID不同) */
    FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_PERSON = 2,   /* 人 */
    FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_CAT = 3,      /* 猫 */
    FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_DOG = 4,      /* 狗 */
    FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_HEDGEHOG = 9, /* 刺猬 */

    /* 静态障碍物(特别注意此处枚举值与目标检测中的类别ID不同) */
    FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_LIMIT = 86,              /*禁区标志*/
    FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_MARK = 87,               /*跨区信标*/
    FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_CHARGESTATION = 88,      /*充电桩*/
    FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_CHARGESTARTIONHEAD = 89, /*充电桩头*/

    /* 危险区域 */
    FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_DOWN_STEP = 201, /* 下沉台阶 */
    FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_PUDDLE = 202,    /* 水坑、泳池、水塘等 */

    // FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_NOT_GRASS = 255 /* 其他非草 */
} fescue_msgs_enum__GrassCellType;

/**
 * @brief 草区域结果，相对相机坐标系的位姿
 */
typedef struct fescue_msgs__msg__GrassRegionResult
{
    /* 栅格地图宽度，1~255 */
    int16_t width;
    /* 栅格地图高度，1~255 */
    int16_t height;
    /* 栅格地图分辨率，单位：厘米 */
    float resolution;
    /* 栅格地图数据 */
    iox::cxx::vector<uint8_t, IOX_MAX_CELL_NUM> cells_array;
} fescue_msgs__msg__GrassRegionResult;

#ifdef __cplusplus
}
#endif
